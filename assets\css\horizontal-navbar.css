/* Horizontal Navbar Styles */
.horizontal-navbar {
    background: var(--bs-body-bg);
    border-bottom: 1px solid var(--bs-border-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1020;
    padding: 0;
}

.horizontal-nav {
    padding: 0.5rem 0;
}

.horizontal-navbar .navbar-brand {
    margin-right: 2rem;
}

.horizontal-navbar .navbar-brand .logo {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.horizontal-navbar .navbar-brand .logo-sm {
    height: 32px;
    width: auto;
    margin-right: 0.5rem;
}

.horizontal-navbar .navbar-brand .logo-lg {
    height: 28px;
    width: auto;
}

/* Navigation Items */
.horizontal-navbar .navbar-nav {
    flex-direction: row;
    align-items: center;
}

.horizontal-navbar .nav-item {
    margin-right: 0.5rem;
}

.horizontal-navbar .nav-link {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    color: var(--bs-body-color);
    text-decoration: none;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.horizontal-navbar .nav-link:hover {
    background-color: var(--bs-primary-bg-subtle);
    color: var(--bs-primary);
}

.horizontal-navbar .nav-link.active {
    background-color: var(--bs-primary);
    color: white;
}

/* Menu Icons */
.horizontal-navbar .menu-icon {
    font-size: 1rem;
    margin-right: 0.5rem;
    width: 16px;
    text-align: center;
}

/* Badges */
.horizontal-navbar .badge {
    font-size: 0.65rem;
    padding: 0.25em 0.5em;
}

/* Dropdown Menus */
.horizontal-navbar .dropdown-menu {
    border: 1px solid var(--bs-border-color);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    padding: 0.5rem 0;
    min-width: 200px;
}

.horizontal-navbar .dropdown-item {
    padding: 0.5rem 1rem;
    color: var(--bs-body-color);
    transition: all 0.2s ease;
}

.horizontal-navbar .dropdown-item:hover {
    background-color: var(--bs-primary-bg-subtle);
    color: var(--bs-primary);
}

/* Investment Widget */
.update-msg-horizontal {
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, var(--bs-success-bg-subtle), var(--bs-info-bg-subtle));
    border-radius: 0.5rem;
    margin-left: 1rem;
}

.update-msg-horizontal .text-success {
    color: var(--bs-success) !important;
    font-weight: 600;
}

/* Mobile Responsive */
@media (max-width: 991.98px) {
    .horizontal-navbar .navbar-collapse {
        background: var(--bs-body-bg);
        border: 1px solid var(--bs-border-color);
        border-radius: 0.5rem;
        margin-top: 0.5rem;
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .horizontal-navbar .navbar-nav {
        flex-direction: column;
        align-items: stretch;
    }
    
    .horizontal-navbar .nav-item {
        margin-right: 0;
        margin-bottom: 0.25rem;
    }
    
    .horizontal-navbar .nav-link {
        justify-content: flex-start;
    }
    
    .horizontal-navbar .dropdown-menu {
        position: static;
        float: none;
        width: 100%;
        margin-top: 0.25rem;
        border: none;
        box-shadow: none;
        background: var(--bs-secondary-bg);
        border-radius: 0.375rem;
    }
    
    .horizontal-navbar .dropdown-item {
        padding-left: 2rem;
    }
    
    .update-msg-horizontal {
        margin-left: 0;
        margin-top: 1rem;
        text-align: center;
    }
}

/* Dark Mode Support */
[data-bs-theme="dark"] .horizontal-navbar {
    background: var(--bs-dark);
    border-bottom-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .horizontal-navbar .nav-link {
    color: var(--bs-light);
}

[data-bs-theme="dark"] .horizontal-navbar .nav-link:hover {
    background-color: var(--bs-primary-bg-subtle);
    color: var(--bs-primary);
}

[data-bs-theme="dark"] .horizontal-navbar .dropdown-menu {
    background: var(--bs-dark);
    border-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .horizontal-navbar .dropdown-item {
    color: var(--bs-light);
}

[data-bs-theme="dark"] .horizontal-navbar .dropdown-item:hover {
    background-color: var(--bs-primary-bg-subtle);
    color: var(--bs-primary);
}

/* Navbar Toggler */
.horizontal-navbar .navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.horizontal-navbar .navbar-toggler:focus {
    box-shadow: none;
}

/* Adjust page content for horizontal navbar */
.page-wrapper {
    margin-left: 0 !important;
    padding-top: 0;
}

/* Hide original sidebar when horizontal navbar is active */
.startbar {
    display: none !important;
}

.startbar-overlay {
    display: none !important;
}

/* Adjust topbar for horizontal layout */
.topbar {
    position: relative;
    z-index: 1019;
}

/* Smooth scrolling for dropdown navigation */
.horizontal-navbar .dropdown-menu {
    max-height: 400px;
    overflow-y: auto;
}

.horizontal-navbar .dropdown-menu::-webkit-scrollbar {
    width: 4px;
}

.horizontal-navbar .dropdown-menu::-webkit-scrollbar-track {
    background: transparent;
}

.horizontal-navbar .dropdown-menu::-webkit-scrollbar-thumb {
    background: var(--bs-border-color);
    border-radius: 2px;
}

.horizontal-navbar .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: var(--bs-secondary);
}
