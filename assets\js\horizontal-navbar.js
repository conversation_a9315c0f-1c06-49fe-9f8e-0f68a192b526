/**
 * Horizontal Navbar JavaScript
 * Handles dropdown functionality and active states for horizontal navigation
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize horizontal navbar functionality
    initHorizontalNavbar();
    
    function initHorizontalNavbar() {
        // Set active navigation item based on current page
        setActiveNavItem();
        
        // Handle dropdown hover effects on desktop
        handleDropdownHover();
        
        // Handle mobile menu toggle
        handleMobileMenu();
        
        // Handle navbar scroll effects
        handleNavbarScroll();
    }
    
    /**
     * Set active navigation item based on current URL
     */
    function setActiveNavItem() {
        const currentPath = window.location.pathname;
        const currentPage = currentPath.split('/').pop() || 'index.html';
        
        // Remove active class from all nav links
        document.querySelectorAll('.horizontal-navbar .nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        // Find and set active nav item
        document.querySelectorAll('.horizontal-navbar .nav-link').forEach(link => {
            const href = link.getAttribute('href');
            if (href && (href === currentPage || href.includes(currentPage))) {
                link.classList.add('active');
                
                // If it's inside a dropdown, also highlight the parent
                const dropdown = link.closest('.dropdown');
                if (dropdown) {
                    const dropdownToggle = dropdown.querySelector('.dropdown-toggle');
                    if (dropdownToggle) {
                        dropdownToggle.classList.add('active');
                    }
                }
            }
        });
        
        // Handle dropdown items
        document.querySelectorAll('.horizontal-navbar .dropdown-item').forEach(item => {
            const href = item.getAttribute('href');
            if (href && (href === currentPage || href.includes(currentPage))) {
                item.classList.add('active');
                
                // Highlight parent dropdown toggle
                const dropdown = item.closest('.dropdown');
                if (dropdown) {
                    const dropdownToggle = dropdown.querySelector('.dropdown-toggle');
                    if (dropdownToggle) {
                        dropdownToggle.classList.add('active');
                    }
                }
            }
        });
    }
    
    /**
     * Handle dropdown hover effects on desktop
     */
    function handleDropdownHover() {
        const dropdowns = document.querySelectorAll('.horizontal-navbar .dropdown');
        
        dropdowns.forEach(dropdown => {
            const dropdownToggle = dropdown.querySelector('.dropdown-toggle');
            const dropdownMenu = dropdown.querySelector('.dropdown-menu');
            
            if (window.innerWidth > 991) {
                // Desktop hover behavior
                dropdown.addEventListener('mouseenter', function() {
                    dropdownMenu.classList.add('show');
                    dropdownToggle.setAttribute('aria-expanded', 'true');
                });
                
                dropdown.addEventListener('mouseleave', function() {
                    dropdownMenu.classList.remove('show');
                    dropdownToggle.setAttribute('aria-expanded', 'false');
                });
            }
        });
    }
    
    /**
     * Handle mobile menu toggle
     */
    function handleMobileMenu() {
        const navbarToggler = document.querySelector('.horizontal-navbar .navbar-toggler');
        const navbarCollapse = document.querySelector('.horizontal-navbar .navbar-collapse');
        
        if (navbarToggler && navbarCollapse) {
            navbarToggler.addEventListener('click', function() {
                const isExpanded = navbarToggler.getAttribute('aria-expanded') === 'true';
                navbarToggler.setAttribute('aria-expanded', !isExpanded);
            });
            
            // Close mobile menu when clicking outside
            document.addEventListener('click', function(event) {
                if (!navbarCollapse.contains(event.target) && !navbarToggler.contains(event.target)) {
                    if (navbarCollapse.classList.contains('show')) {
                        const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                        bsCollapse.hide();
                    }
                }
            });
        }
    }
    
    /**
     * Handle navbar scroll effects
     */
    function handleNavbarScroll() {
        const navbar = document.querySelector('.horizontal-navbar');
        let lastScrollTop = 0;
        
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop > lastScrollTop && scrollTop > 100) {
                // Scrolling down
                navbar.style.transform = 'translateY(-100%)';
            } else {
                // Scrolling up
                navbar.style.transform = 'translateY(0)';
            }
            
            lastScrollTop = scrollTop;
        });
    }
    
    /**
     * Handle window resize events
     */
    window.addEventListener('resize', function() {
        // Reinitialize dropdown hover behavior based on screen size
        handleDropdownHover();
        
        // Close mobile menu if window is resized to desktop
        if (window.innerWidth > 991) {
            const navbarCollapse = document.querySelector('.horizontal-navbar .navbar-collapse');
            if (navbarCollapse && navbarCollapse.classList.contains('show')) {
                const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                bsCollapse.hide();
            }
        }
    });
    
    /**
     * Smooth scroll for anchor links
     */
    document.querySelectorAll('.horizontal-navbar a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    /**
     * Add loading states for navigation links
     */
    document.querySelectorAll('.horizontal-navbar .nav-link, .horizontal-navbar .dropdown-item').forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href && href !== '#' && !href.startsWith('#') && !href.startsWith('javascript:')) {
                // Add loading state
                this.style.opacity = '0.6';
                this.style.pointerEvents = 'none';
                
                // Remove loading state after a short delay (in case navigation is fast)
                setTimeout(() => {
                    this.style.opacity = '';
                    this.style.pointerEvents = '';
                }, 1000);
            }
        });
    });
    
});

/**
 * Theme toggle support for horizontal navbar
 */
function updateHorizontalNavbarTheme() {
    const navbar = document.querySelector('.horizontal-navbar');
    const currentTheme = document.documentElement.getAttribute('data-bs-theme');
    
    if (navbar) {
        navbar.setAttribute('data-theme', currentTheme);
    }
}

// Listen for theme changes
const themeObserver = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-bs-theme') {
            updateHorizontalNavbarTheme();
        }
    });
});

themeObserver.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['data-bs-theme']
});

// Initialize theme on load
updateHorizontalNavbarTheme();
