var options={chart:{height:205,type:"donut"},plotOptions:{pie:{donut:{size:"70%"}}},dataLabels:{enabled:!1},stroke:{show:!0,width:2,colors:["transparent"]},series:[50,25],legend:{show:!0,position:"bottom",horizontalAlign:"center",verticalAlign:"middle",floating:!1,fontSize:"13px",fontFamily:"Be Vietnam Pro, sans-serif",offsetX:0,offsetY:0},labels:["Income","Expense"],colors:["#2b3eb1","#fc7100"],responsive:[{breakpoint:600,options:{plotOptions:{donut:{customScale:.2}},chart:{height:240},legend:{show:!1}}}],tooltip:{y:{formatter:function(e){return e+" %"}}}},chart=new ApexCharts(document.querySelector("#customers"),options);chart.render();