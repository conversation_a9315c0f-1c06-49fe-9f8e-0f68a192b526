!function(t,n){"function"==typeof define&&define.amd?define(["raphael"],function(e){return t.JustGage=n(e)}):"object"==typeof module&&module.exports?module.exports=t.JustGage=n(require("raphael")):t.JustGage=n(<PERSON>)}("undefined"!=typeof window?window:this,function(t){const n=function(n){const e=this;if(e.events={},o(n))return!1;if(o(n.id)){if(o(n.parentNode))return!1;e.node=n.parentNode}else if(e.node=document.getElementById(n.id),!e.node)return!1;const r=e.node.dataset?e.node.dataset:{},c=!o(n.defaults)&&n.defaults;let u,d,p,x,h,b,M,v,y,F,S,w,A,z,V,T,C,N,O;!1!==c&&delete(n=function(t){t=t||{};for(let n=1;n<arguments.length;n++)if(arguments[n])for(const e in arguments[n])Object.prototype.hasOwnProperty.call(arguments[n],e)&&(t[e]=arguments[n][e]);return t}({},n,c)).defaults,e.config={id:n.id,classId:"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){const n=16*Math.random()|0,e="x"===t?n:3&n|8;return e.toString(16)}),value:i("value",n,r,0,"float"),defaults:i("defaults",n,r,0,!1),parentNode:i("parentNode",n,r,null),width:i("width",n,r,null),height:i("height",n,r,null),valueFontColor:i("valueFontColor",n,r,"#010101"),valueFontFamily:i("valueFontFamily",n,r,"Arial"),symbol:i("symbol",n,r,""),min:i("min",n,r,0,"float"),minTxt:i("minTxt",n,r,!1),max:i("max",n,r,100,"float"),maxTxt:i("maxTxt",n,r,!1),reverse:i("reverse",n,r,!1),humanFriendlyDecimal:i("humanFriendlyDecimal",n,r,0),textRenderer:i("textRenderer",n,r,null),onAnimationEnd:i("onAnimationEnd",n,r,null),gaugeWidthScale:i("gaugeWidthScale",n,r,1),gaugeColor:i("gaugeColor",n,r,"#edebeb"),label:i("label",n,r,""),labelFontColor:i("labelFontColor",n,r,"#b3b3b3"),labelFontFamily:i("labelFontFamily",n,r,"Arial"),shadowOpacity:i("shadowOpacity",n,r,.2),shadowSize:i("shadowSize",n,r,5),shadowVerticalOffset:i("shadowVerticalOffset",n,r,3),levelColors:i("levelColors",n,r,["#a9d70b","#f9c802","#ff0000"],"array",","),startAnimationTime:i("startAnimationTime",n,r,700),startAnimationType:i("startAnimationType",n,r,">"),refreshAnimationTime:i("refreshAnimationTime",n,r,700),refreshAnimationType:i("refreshAnimationType",n,r,">"),donutStartAngle:i("donutStartAngle",n,r,90),valueMinFontSize:i("valueMinFontSize",n,r,16),labelMinFontSize:i("labelMinFontSize",n,r,10),minLabelMinFontSize:i("minLabelMinFontSize",n,r,10),maxLabelMinFontSize:i("maxLabelMinFontSize",n,r,10),hideValue:i("hideValue",n,r,!1),hideMinMax:i("hideMinMax",n,r,!1),showInnerShadow:i("showInnerShadow",n,r,!1),humanFriendly:i("humanFriendly",n,r,!1),noGradient:i("noGradient",n,r,!1),donut:i("donut",n,r,!1),differential:i("differential",n,r,!1),relativeGaugeSize:i("relativeGaugeSize",n,r,!1),counter:i("counter",n,r,!1),decimals:i("decimals",n,r,0),customSectors:i("customSectors",n,r,{}),formatNumber:i("formatNumber",n,r,!1),pointer:i("pointer",n,r,!1),pointerOptions:i("pointerOptions",n,r,{}),displayRemaining:i("displayRemaining",n,r,!1)},e.config.value>e.config.max&&(e.config.value=e.config.max),e.config.value<e.config.min&&(e.config.value=e.config.min),e.originalValue=i("value",n,r,-1,"float"),null!==e.config.id&&null!==document.getElementById(e.config.id)?e.canvas=t(e.config.id,"100%","100%"):null!==e.config.parentNode&&(e.canvas=t(e.config.parentNode,"100%","100%")),!0===e.config.relativeGaugeSize?!0===e.config.donut?(e.canvas.setViewBox(0,0,200,200,!0),u=200,d=200):(e.canvas.setViewBox(0,0,200,100,!0),u=200,d=100):null!==e.config.width&&null!==e.config.height?(u=e.config.width,d=e.config.height):null!==e.config.parentNode?(e.canvas.setViewBox(0,0,200,100,!0),u=200,d=100):(u=1*m(document.getElementById(e.config.id),"width").slice(0,-2),d=1*m(document.getElementById(e.config.id),"height").slice(0,-2)),!0===e.config.donut?(u>d?p=x=d:x=p=u,b=(u-p)/2,M=(d-x)/2,v=x/6.4>16?x/5.4:18,y=b+p/2,w=b+p/2,A=(F=""!==e.config.label?M+x/1.85:M+x/1.7)+(S=x/16>10?x/16:10),z=x/16>10?x/16:10,V=b+p/10+p/6.666666666666667*e.config.gaugeWidthScale/2,T=A,C=x/16>10?x/16:10,N=b+p-p/10-p/6.666666666666667*e.config.gaugeWidthScale/2,O=A):(u>d?(p=2*(x=d))>u&&(p/=h=p/u,x/=h):x=u<d?(p=u)/2:.5*(p=u),b=(u-p)/2,M=(d-x)/2,v=x/6.5>e.config.valueMinFontSize?x/6.5:e.config.valueMinFontSize,y=b+p/2,F=M+x/1.275,S=x/16>e.config.labelMinFontSize?x/16:e.config.labelMinFontSize,w=b+p/2,A=F+v/2+5,z=x/16>e.config.minLabelMinFontSize?x/16:e.config.minLabelMinFontSize,V=b+p/10+p/6.666666666666667*e.config.gaugeWidthScale/2,T=A,C=x/16>e.config.maxLabelMinFontSize?x/16:e.config.maxLabelMinFontSize,N=b+p-p/10-p/6.666666666666667*e.config.gaugeWidthScale/2,O=A),e.params={canvasW:u,canvasH:d,widgetW:p,widgetH:x,dx:b,dy:M,valueFontSize:v,valueX:y,valueY:F,labelFontSize:S,labelX:w,labelY:A,minFontSize:z,minX:V,minY:T,maxFontSize:C,maxX:N,maxY:O},e.canvas.customAttributes.pki=function(t,n){let i=e.config.min,o=e.config.max;const a=e.params.widgetW,l=e.params.widgetH,r=e.params.dx,c=e.params.dy,f=e.config.gaugeWidthScale,s=e.config.donut;let m,g,u,d,p,x,h,b,M,v,y,F;if(i<0&&!n&&(o-=i,t-=i,i=0),s)return m=(1-2*(t-i)/(o-i))*Math.PI,u=(g=a/2-a/30)-a/6.666666666666667*f,p=l/2+c,b=(d=a/2+r)+g*Math.cos(m),M=p-g*Math.sin(m),v=d+u*Math.cos(m),y=p-u*Math.sin(m),F="M"+(d-u)+","+p+" ",F+="L"+(d-g)+","+p+" ",t-i>(o-i)/2&&(F+="A"+g+","+g+" 0 0 1 "+(d+g)+","+p+" "),F+="A"+g+","+g+" 0 0 1 "+b+","+M+" ",F+="L"+v+","+y+" ",t-i>(o-i)/2&&(F+="A"+u+","+u+" 0 0 0 "+(d+u)+","+p+" "),F+="A"+u+","+u+" 0 0 0 "+(d-u)+","+p+" ",{path:F+="Z "};if(n){m=(1-(t-i)/(o-i))*Math.PI,u=(g=a/2-a/10)-a/6.666666666666667*f,p=l/1.25+c,b=(d=a/2+r)+g*Math.cos(m),M=p-g*Math.sin(m),v=d+u*Math.cos(m),y=p-u*Math.sin(m);const n=i+(o-i)/2;return F="M"+d+","+(p-u)+" ",F+="L"+d+","+(p-g)+" ",F+="A"+g+","+g+" 0 0 "+(h=t<n?0:1)+" "+b+","+M+" ",F+="L"+v+","+y+" ",F+="A"+u+","+u+" 0 0 "+(x=t<n?1:0)+" "+d+","+(p-u)+" ",{path:F+="Z "}}return m=(1-(t-i)/(o-i))*Math.PI,F="M"+((d=a/2+r)-(u=(g=a/2-a/10)-a/6.666666666666667*f))+","+(p=l/1.25+c)+" ",F+="L"+(d-g)+","+p+" ",F+="A"+g+","+g+" 0 0 1 "+(b=d+g*Math.cos(m))+","+(M=p-g*Math.sin(m))+" ",F+="L"+(v=d+u*Math.cos(m))+","+(y=p-u*Math.sin(m))+" ",F+="A"+u+","+u+" 0 0 0 "+(d-u)+","+p+" ",{path:F+="Z "}},e.canvas.customAttributes.ndl=function(t){const n=e.config.min,i=e.config.max,o=e.params.widgetW,a=e.params.widgetH,l=e.params.dx,r=e.params.dy,c=e.config.gaugeWidthScale,f=e.config.donut;let s,m,g,u,d,p,x,h,b,M,v,y,F,S,w,A,z,V=3.5*o/100,T=o/15,C=o/100;return null!=e.config.pointerOptions.toplength&&void 0!==e.config.pointerOptions.toplength&&(V=e.config.pointerOptions.toplength),null!=e.config.pointerOptions.bottomlength&&void 0!==e.config.pointerOptions.bottomlength&&(T=e.config.pointerOptions.bottomlength),null!=e.config.pointerOptions.bottomwidth&&void 0!==e.config.pointerOptions.bottomwidth&&(C=e.config.pointerOptions.bottomwidth),f?(s=(1-2*(t-n)/(i-n))*Math.PI,g=(m=o/2-o/30)-o/6.666666666666667*c,u=a/2+r,d=o/2+l+m*Math.cos(s),p=a-(a-u)-m*Math.sin(s),x=o/2+l+g*Math.cos(s),h=a-(a-u)-g*Math.sin(s),b=d+V*Math.cos(s),M=p-V*Math.sin(s),v=x-T*Math.cos(s),y=h+T*Math.sin(s),z="M"+(F=v+C*Math.sin(s))+","+(S=y+C*Math.cos(s))+" ",z+="L"+(w=v-C*Math.sin(s))+","+(A=y-C*Math.cos(s))+" ",z+="L"+b+","+M+" ",{path:z+="Z "}):(s=(1-(t-n)/(i-n))*Math.PI,g=(m=o/2-o/10)-o/6.666666666666667*c,u=a/1.25+r,d=o/2+l+m*Math.cos(s),p=a-(a-u)-m*Math.sin(s),x=o/2+l+g*Math.cos(s),h=a-(a-u)-g*Math.sin(s),b=d+V*Math.cos(s),M=p-V*Math.sin(s),v=x-T*Math.cos(s),y=h+T*Math.sin(s),z="M"+(F=v+C*Math.sin(s))+","+(S=y+C*Math.cos(s))+" ",z+="L"+(w=v-C*Math.sin(s))+","+(A=y-C*Math.cos(s))+" ",z+="L"+b+","+M+" ",{path:z+="Z "})},e.gauge=e.canvas.path().attr({stroke:"none",fill:e.config.gaugeColor,pki:[e.config.max]}),e.level=e.canvas.path().attr({stroke:"none",fill:a(e.config.value,(e.config.value-e.config.min)/(e.config.max-e.config.min),e.config.levelColors,e.config.noGradient,e.config.customSectors),pki:[e.config.differential?0:e.config.min,e.config.differential]}),e.config.donut&&e.level.transform("r"+e.config.donutStartAngle+", "+(e.params.widgetW/2+e.params.dx)+", "+(e.params.widgetH/2+e.params.dy)),e.config.pointer&&(e.needle=e.canvas.path().attr({stroke:o(e.config.pointerOptions.stroke)?"none":e.config.pointerOptions.stroke,"stroke-width":o(e.config.pointerOptions.stroke_width)?0:e.config.pointerOptions.stroke_width,"stroke-linecap":o(e.config.pointerOptions.stroke_linecap)?"square":e.config.pointerOptions.stroke_linecap,fill:o(e.config.pointerOptions.color)?"#000000":e.config.pointerOptions.color,ndl:[e.config.min]}),e.config.donut&&e.needle.transform("r"+e.config.donutStartAngle+", "+(e.params.widgetW/2+e.params.dx)+", "+(e.params.widgetH/2+e.params.dy))),e.txtValue=e.canvas.text(e.params.valueX,e.params.valueY,0),e.txtValue.attr({"font-size":e.params.valueFontSize,"font-weight":"bold","font-family":e.config.valueFontFamily,fill:e.config.valueFontColor,"fill-opacity":"0"}),l(e.txtValue,e.params.valueFontSize,e.params.valueY),e.txtLabel=e.canvas.text(e.params.labelX,e.params.labelY,e.config.label),e.txtLabel.attr({"font-size":e.params.labelFontSize,"font-weight":"normal","font-family":e.config.labelFontFamily,fill:e.config.labelFontColor,"fill-opacity":"0"}),l(e.txtLabel,e.params.labelFontSize,e.params.labelY);let L=e.config.min;e.config.reverse&&(L=e.config.max),e.txtMinimum=L,e.config.minTxt?e.txtMinimum=e.config.minTxt:e.config.humanFriendly?e.txtMinimum=f(L,e.config.humanFriendlyDecimal):e.config.formatNumber&&(e.txtMinimum=s(L)),e.txtMin=e.canvas.text(e.params.minX,e.params.minY,e.txtMinimum),e.txtMin.attr({"font-size":e.params.minFontSize,"font-weight":"normal","font-family":e.config.labelFontFamily,fill:e.config.labelFontColor,"fill-opacity":e.config.hideMinMax||e.config.donut?"0":"1"}),l(e.txtMin,e.params.minFontSize,e.params.minY);let E=e.config.max;e.config.reverse&&(E=e.config.min),e.txtMaximum=E,e.config.maxTxt?e.txtMaximum=e.config.maxTxt:e.config.humanFriendly?e.txtMaximum=f(E,e.config.humanFriendlyDecimal):e.config.formatNumber&&(e.txtMaximum=s(E)),e.txtMax=e.canvas.text(e.params.maxX,e.params.maxY,e.txtMaximum),e.txtMax.attr({"font-size":e.params.maxFontSize,"font-weight":"normal","font-family":e.config.labelFontFamily,fill:e.config.labelFontColor,"fill-opacity":e.config.hideMinMax||e.config.donut?"0":"1"}),l(e.txtMax,e.params.maxFontSize,e.params.maxY);const k=e.canvas.canvas.childNodes[1],Y="http://www.w3.org/2000/svg";if(void 0!==g&&g<9||(void 0!==g?function t(n){void 0!==document.createElementNS?n():setTimeout(function(){t(n)},100)}(function(){e.generateShadow(Y,k)}):e.generateShadow(Y,k)),e.config.textRenderer&&!1!==e.config.textRenderer(e.originalValue)?e.originalValue=e.config.textRenderer(e.originalValue):e.config.humanFriendly?e.originalValue=f(e.originalValue,e.config.humanFriendlyDecimal)+e.config.symbol:e.config.formatNumber?e.originalValue=s(e.originalValue)+e.config.symbol:e.config.displayRemaining?e.originalValue=(1*(e.config.max-e.originalValue)).toFixed(e.config.decimals)+e.config.symbol:e.originalValue=(1*e.originalValue).toFixed(e.config.decimals)+e.config.symbol,!0===e.config.counter){const t=function(){let t=e.level.attr("pki")[0];e.config.reverse&&(t=1*e.config.max+1*e.config.min-1*e.level.attr("pki")[0]),e.config.textRenderer&&!1!==e.config.textRenderer(Math.floor(t))?e.txtValue.attr("text",e.config.textRenderer(Math.floor(t))):e.config.humanFriendly?e.txtValue.attr("text",f(Math.floor(t),e.config.humanFriendlyDecimal)+e.config.symbol):e.config.formatNumber?e.txtValue.attr("text",s(Math.floor(t))+e.config.symbol):e.config.displayRemaining?e.txtValue.attr("text",(1*(e.config.max-t)).toFixed(e.config.decimals)+e.config.symbol):e.txtValue.attr("text",(1*t).toFixed(e.config.decimals)+e.config.symbol),l(e.txtValue,e.params.valueFontSize,e.params.valueY),t=null},n=function(){e.txtValue.attr({text:e.originalValue}),l(e.txtValue,e.params.valueFontSize,e.params.valueY)};this.bindEvent("raphael.anim.finish",n),this.bindEvent("raphael.anim.frame",t)}else{const t=function(){e.txtValue.attr({text:e.originalValue}),l(e.txtValue,e.params.valueFontSize,e.params.valueY)};this.bindEvent("raphael.anim.start",t)}let I=e.config.value;e.config.reverse&&(I=1*e.config.max+1*e.config.min-1*e.config.value),e.level.animate({pki:[I,e.config.differential]},e.config.startAnimationTime,e.config.startAnimationType,e.config.onAnimationEnd),e.config.pointer&&e.needle.animate({ndl:[I]},e.config.startAnimationTime,e.config.startAnimationType),e.txtValue.animate({"fill-opacity":e.config.hideValue?"0":"1"},e.config.startAnimationTime,e.config.startAnimationType),e.txtLabel.animate({"fill-opacity":"1"},e.config.startAnimationTime,e.config.startAnimationType)};function e(t,n,e){switch(n){case"valueFontColor":if(!c(e))break;t.txtValue.attr({fill:e});break;case"labelFontColor":if(!c(e))break;t.txtMin.attr({fill:e}),t.txtMax.attr({fill:e}),t.txtLabel.attr({fill:e})}}function i(t,n,e,i,a,l){let r=i,c=!1;if(!o(t)&&(!o(e)&&"object"==typeof e&&t in e?(r=e[t],c=!0):!o(n)&&"object"==typeof n&&t in n?(r=n[t],c=!0):r=i,!0===c&&!o(a)))switch(a){case"int":r=parseInt(r,10);break;case"float":r=parseFloat(r)}return r}function o(t){return null===t||void 0===t}function a(t,n,e,i,o){let a,l,c,f,s,m,g,u,d,p,x;const h=o&&o.ranges&&o.ranges.length>0;if(i=i||h,h){!0===o.percents&&(t=100*n);for(let n=0;n<o.ranges.length;n++)if(t>=o.ranges[n].lo&&t<=o.ranges[n].hi)return o.ranges[n].color}const b=e.length;if(1===b)return e[0];const M=i?1/b:1/(b-1),v=[];for(let t=0;t<e.length;t++)a=i?M*(t+1):M*t,l=parseInt(r(e[t]).substring(0,2),16),c=parseInt(r(e[t]).substring(2,4),16),f=parseInt(r(e[t]).substring(4,6),16),v[t]={pct:a,color:{r:l,g:c,b:f}};if(0===n)return"rgb("+[v[0].color.r,v[0].color.g,v[0].color.b].join(",")+")";for(let t=0;t<v.length;t++)if(n<=v[t].pct)return i?"rgb("+[v[t].color.r,v[t].color.g,v[t].color.b].join(",")+")":(s=v[t-1],g=(m=v[t]).pct-s.pct,d=1-(u=(n-s.pct)/g),p=u,"rgb("+[(x={r:Math.floor(s.color.r*d+m.color.r*p),g:Math.floor(s.color.g*d+m.color.g*p),b:Math.floor(s.color.b*d+m.color.b*p)}).r,x.g,x.b].join(",")+")")}function l(t,n,e){(!g||g>9)&&t.node.firstChild.attributes.dy&&(t.node.firstChild.attributes.dy.value=0)}function r(t){return"#"===t.charAt(0)?t.substring(1,7):t}function c(t){return"string"==typeof t&&/^#([0-9A-Fa-f]{3}){1,2}$/.test(t)}function f(t,n){const e=Math.pow(10,n),i=" KMGTPE";let o=0;for(;(t>=1e3||t<=-1e3)&&++o<i.length;)t/=1e3;return o=o>=i.length?i.length-1:o,Math.round(t*e)/e+i[o]}function s(t){const n=t.toString().split(".");return n[0]=n[0].replace(/\B(?=(\d{3})+(?!\d))/g,","),n.join(".")}function m(t,n){let e="";return document.defaultView&&document.defaultView.getComputedStyle?e=document.defaultView.getComputedStyle(t,"").getPropertyValue(n):t.currentStyle&&(n=n.replace(/-(\w)/g,function(t,n){return n.toUpperCase()}),e=t.currentStyle[n]),e}n.prototype.bindEvent=function(n,e){n+="."+this.level.id,this.events[n]&&t.eve.off(n,this.events[n]),t.eve.on(n,e),this.events[n]=e},n.prototype.refresh=function(t,n,e,i){const o=this;let r;n=u(n)?n:null,e=u(e)?e:null,null!==(i=i||null)&&(o.config.label=i,o.txtLabel.attr({text:o.config.label}),l(o.txtLabel,o.params.labelFontSize,o.params.labelY)),null!==e&&(o.config.min=e,o.txtMinimum=o.config.min,o.config.minTxt?o.txtMinimum=o.config.minTxt:o.config.humanFriendly?o.txtMinimum=f(o.config.min,o.config.humanFriendlyDecimal):o.config.formatNumber&&(o.txtMinimum=s(o.config.min)),o.config.reverse?(o.txtMax.attr({text:o.txtMinimum}),l(o.txtMax,o.params.minFontSize,o.params.minY)):(o.txtMin.attr({text:o.txtMinimum}),l(o.txtMin,o.params.minFontSize,o.params.minY))),null!==n&&(o.config.max=n,o.txtMaximum=o.config.max,o.config.maxTxt?o.txtMaximum=o.config.maxTxt:o.config.humanFriendly?o.txtMaximum=f(o.config.max,o.config.humanFriendlyDecimal):o.config.formatNumber&&(o.txtMaximum=s(o.config.max)),o.config.reverse?(o.txtMin.attr({text:o.txtMaximum}),l(o.txtMin,o.params.maxFontSize,o.params.maxY)):(o.txtMax.attr({text:o.txtMaximum}),l(o.txtMax,o.params.maxFontSize,o.params.maxY))),r=t,1*t>1*o.config.max&&(t=1*o.config.max),1*t<1*o.config.min&&(t=1*o.config.min);const c=a(t,(t-o.config.min)/(o.config.max-o.config.min),o.config.levelColors,o.config.noGradient,o.config.customSectors);r=o.config.textRenderer&&!1!==o.config.textRenderer(r)?o.config.textRenderer(r):o.config.humanFriendly?f(r,o.config.humanFriendlyDecimal)+o.config.symbol:o.config.formatNumber?s((1*r).toFixed(o.config.decimals))+o.config.symbol:o.config.displayRemaining?(1*(o.config.max-r)).toFixed(o.config.decimals)+o.config.symbol:(1*r).toFixed(o.config.decimals)+o.config.symbol,o.originalValue=r,o.config.value=1*t,o.config.counter||(o.txtValue.attr({text:r}),l(o.txtValue,o.params.valueFontSize,o.params.valueY));let m=o.config.value;o.config.reverse&&(m=1*o.config.max+1*o.config.min-1*o.config.value),o.level.animate({pki:[m,o.config.differential],fill:c},o.config.refreshAnimationTime,o.config.refreshAnimationType,o.config.onAnimationEnd),o.config.pointer&&o.needle.animate({ndl:[m]},o.config.refreshAnimationTime,o.config.refreshAnimationType)},n.prototype.update=function(t,n){const i=this;if(t instanceof Object)for(const o in t)e(i,o,n=t[o]);else e(i,t,n)},n.prototype.destroy=function(){this.node&&this.node.parentNode&&(this.node.innerHTML="");for(const n in this.events)t.eve.off(n,this.events[n]);this.events={}},n.prototype.generateShadow=function(t,n){const e=this,i="inner-shadow-"+(e.config.id||e.config.classId),o=document.createElementNS(t,"filter");o.setAttribute("id",i),n.appendChild(o);const a=document.createElementNS(t,"feOffset");a.setAttribute("dx",0),a.setAttribute("dy",e.config.shadowVerticalOffset),o.appendChild(a);const l=document.createElementNS(t,"feGaussianBlur");l.setAttribute("result","offset-blur"),l.setAttribute("stdDeviation",e.config.shadowSize),o.appendChild(l);const r=document.createElementNS(t,"feComposite");r.setAttribute("operator","out"),r.setAttribute("in","SourceGraphic"),r.setAttribute("in2","offset-blur"),r.setAttribute("result","inverse"),o.appendChild(r);const c=document.createElementNS(t,"feFlood");c.setAttribute("flood-color","black"),c.setAttribute("flood-opacity",e.config.shadowOpacity),c.setAttribute("result","color"),o.appendChild(c);const f=document.createElementNS(t,"feComposite");f.setAttribute("operator","in"),f.setAttribute("in","color"),f.setAttribute("in2","inverse"),f.setAttribute("result","shadow"),o.appendChild(f);const s=document.createElementNS(t,"feComposite");s.setAttribute("operator","over"),s.setAttribute("in","shadow"),s.setAttribute("in2","SourceGraphic"),o.appendChild(s),e.config.showInnerShadow&&(e.canvas.canvas.childNodes[2].setAttribute("filter","url(#"+i+")"),e.canvas.canvas.childNodes[3].setAttribute("filter","url(#"+i+")"))};const g=function(){let t=3;const n=document.createElement("div"),e=n.getElementsByTagName("i");for(;e[0];)n.innerHTML="\x3c!--[if gt IE "+ ++t+"]><i></i><![endif]--\x3e";return t>4?t:void 0}();function u(t){return null!==t&&void 0!==t&&!isNaN(t)}return n});
//# sourceMappingURL=justgage.min.js.map