(function(a,b){"object"==typeof exports&&"undefined"!=typeof module?module.exports=b():"function"==typeof define&&define.amd?define(b):(a=a||self,a.Tobii=b())})(this,function(){'use strict';return function(a){var b=Math.abs,c={},d=window,e=0,f=null,g=null,h=null,i=null,j=null,k={},l=!1,m=!1,n=!1,o=null,p=null,q=null,r=null,s=null,t=!1,u=!1,v=[],w=[],x=0,y={gallery:[],slider:null,sliderElements:[],elementsLength:0,currentIndex:0,x:0},z={},A=null,B=null,C=function(a){var b={selector:".lightbox",captions:!0,captionsSelector:"img",captionAttribute:"alt",nav:"auto",navText:["<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewbox=\"0 0 24 24\" aria-hidden=\"true\" focusable=\"false\"><path d=\"M14 18l-6-6 6-6\"/></svg>","<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewbox=\"0 0 24 24\" aria-hidden=\"true\" focusable=\"false\"><path d=\"M10 6l6 6-6 6\"/></svg>"],navLabel:["Previous image","Next image"],close:!0,closeText:"<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewbox=\"0 0 24 24\" aria-hidden=\"true\" focusable=\"false\"><path d=\"M6 6l12 12M6 18L18 6\"/></svg>",closeLabel:"Close lightbox",loadingIndicatorLabel:"Image loading",counter:!0,download:!1,downloadText:"",downloadLabel:"Download image",keyboard:!0,zoom:!0,zoomText:"<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" aria-hidden=\"true\" focusable=\"false\"><path d=\"M21 16v5h-5\"/><path d=\"M8 21H3v-5\"/><path d=\"M16 3h5v5\"/><path d=\"M3 8V3h5\"/></svg>",docClose:!0,swipeClose:!0,hideScrollbar:!0,draggable:!0,threshold:100,rtl:!1,loop:!1,autoplayVideo:!1};return a&&Object.keys(a).forEach(function(c){b[c]=a[c]}),b},D={image:{checkSupport:function(a){return!a.hasAttribute("data-type")&&a.href.match(/\.(png|jpe?g|tiff|tif|gif|bmp|webp|svg|ico)(\?.*)?$/i)},init:function(a,b){var d=document.createElement("figure"),f=document.createElement("figcaption"),g=document.createElement("img"),h=a.querySelector("img"),i=document.createElement("div");d.style.opacity="0",h&&(g.alt=h.alt||""),g.setAttribute("src",""),g.setAttribute("data-src",a.href),d.appendChild(g),c.captions&&("self"===c.captionsSelector&&a.getAttribute(c.captionAttribute)?f.textContent=a.getAttribute(c.captionAttribute):"img"===c.captionsSelector&&h&&h.getAttribute(c.captionAttribute)&&(f.textContent=h.getAttribute(c.captionAttribute)),f.textContent&&(f.id="tobii-figcaption-"+e,d.appendChild(f),g.setAttribute("aria-labelledby",f.id),++e)),b.appendChild(d),i.className="tobii-loader",i.setAttribute("role","progressbar"),i.setAttribute("aria-label",c.loadingIndicatorLabel),b.appendChild(i),b.setAttribute("data-type","image")},onPreload:function(a){D.image.onLoad(a)},onLoad:function(a){var b=a.querySelector("img");if(b.hasAttribute("data-src")){var c=a.querySelector("figure"),d=a.querySelector(".tobii-loader");b.onload=function(){a.removeChild(d),c.style.opacity="1"},b.setAttribute("src",b.getAttribute("data-src")),b.removeAttribute("data-src")}},onLeave:function(){},onCleanup:function(){}},html:{checkSupport:function(a){return la(a,"html")},init:function(a,b){var c=a.hasAttribute("href")?a.getAttribute("href"):a.getAttribute("data-target"),d=document.querySelector(c);if(!d)throw new Error("Ups, I can't find the target "+c+".");b.appendChild(d),b.setAttribute("data-type","html")},onPreload:function(){},onLoad:function(a){var b=a.querySelector("video");b&&(b.hasAttribute("data-time")&&0<b.readyState&&(b.currentTime=b.getAttribute("data-time")),c.autoplayVideo&&b.play())},onLeave:function(a){var b=a.querySelector("video");b&&(!b.paused&&b.pause(),0<b.readyState&&b.setAttribute("data-time",b.currentTime))},onCleanup:function(a){var b=a.querySelector("video");if(b&&0<b.readyState&&3>b.readyState&&b.duration!==b.currentTime){var c=b.cloneNode(!0);ma(b),b.load(),b.parentNode.removeChild(b),a.appendChild(c)}}},iframe:{checkSupport:function(a){return la(a,"iframe")},init:function(a,b){var c=document.createElement("iframe"),d=a.hasAttribute("href")?a.getAttribute("href"):a.getAttribute("data-target");c.setAttribute("frameborder","0"),c.setAttribute("src",""),c.setAttribute("data-src",d),a.getAttribute("data-width")&&(c.style.maxWidth=a.getAttribute("data-width")+"px"),a.getAttribute("data-height")&&(c.style.maxHeight=a.getAttribute("data-height")+"px"),b.appendChild(c),b.setAttribute("data-type","iframe")},onPreload:function(){},onLoad:function(a){var b=a.querySelector("iframe");b.setAttribute("src",b.getAttribute("data-src"))},onLeave:function(){},onCleanup:function(){}},youtube:{checkSupport:function(a){return la(a,"youtube")},init:function(a,b){var c=document.createElement("div");b.appendChild(c),w[x]=new window.YT.Player(c,{host:"https://www.youtube-nocookie.com",height:a.getAttribute("data-height")||"360",width:a.getAttribute("data-width")||"640",videoId:a.getAttribute("data-id"),playerVars:{controls:a.getAttribute("data-controls")||1,rel:0,playsinline:1}}),b.setAttribute("data-player",x),b.setAttribute("data-type","youtube"),x++},onPreload:function(){},onLoad:function(a){c.autoplayVideo&&w[a.getAttribute("data-player")].playVideo()},onLeave:function(a){1===w[a.getAttribute("data-player")].getPlayerState()&&w[a.getAttribute("data-player")].pauseVideo()},onCleanup:function(a){1===w[a.getAttribute("data-player")].getPlayerState()&&w[a.getAttribute("data-player")].pauseVideo()}}};Object.entries||(Object.entries=function(a){for(var b=Object.keys(a),c=b.length,d=Array(c);c--;)d[c]=[b[c],a[b[c]]];return d});var E=function(a){c=C(a),f||K();var b=document.querySelectorAll(c.selector);if(!b)throw new Error("Ups, I can't find the selector "+c.selector+".");Array.prototype.forEach.call(b,function(a){F(a)})},F=function(a,b){if(null!==document.querySelector("[data-type=\"youtube\"]")&&!u){if(null===document.getElementById("iframe_api")){var c=document.createElement("script"),d=document.getElementsByTagName("script")[0];c.id="iframe_api",c.src="https://www.youtube.com/iframe_api",d.parentNode.insertBefore(c,d)}-1===v.indexOf(a)&&v.push(a),window.onYouTubePlayerAPIReady=function(){Array.prototype.forEach.call(v,function(a){I(a,b)}),u=!0}}else I(a,b)},G=function(a){return a.hasAttribute("data-group")?a.getAttribute("data-group"):"default"},H=function(a){return JSON.parse(JSON.stringify(a))},I=function(a,b){if(A=G(a),Object.prototype.hasOwnProperty.call(z,A)||(z[A]=H(y),L()),-1===z[A].gallery.indexOf(a)){if(z[A].gallery.push(a),z[A].elementsLength++,c.zoom&&a.querySelector("img")){var d=document.createElement("div");d.className="tobii-zoom__icon",d.innerHTML=c.zoomText,a.classList.add("tobii-zoom"),a.appendChild(d)}a.addEventListener("click",_),M(a),ra()&&A===B&&(na(),pa()),b&&b.call(this)}else throw new Error("Ups, element already added to the lightbox.")},J=function(a,b){var d=G(a);if(-1===z[d].gallery.indexOf(a));else{var e=z[d].gallery.indexOf(a),f=z[d].sliderElements[e];if(z[d].elementsLength--,c.zoom&&a.querySelector(".tobii-zoom__icon")){var g=a.querySelector(".tobii-zoom__icon");g.parentNode.classList.remove("tobii-zoom"),g.parentNode.removeChild(g)}a.removeEventListener("click",_),f.parentNode.removeChild(f),ra()&&d===B&&(na(),pa()),b&&b.call(this)}},K=function(){f=document.createElement("div"),f.setAttribute("role","dialog"),f.setAttribute("aria-hidden","true"),f.className="tobii",g=document.createElement("button"),g.className="tobii__prev",g.setAttribute("type","button"),g.setAttribute("aria-label",c.navLabel[0]),g.innerHTML=c.navText[0],f.appendChild(g),h=document.createElement("button"),h.className="tobii__next",h.setAttribute("type","button"),h.setAttribute("aria-label",c.navLabel[1]),h.innerHTML=c.navText[1],f.appendChild(h),i=document.createElement("button"),i.className="tobii__close",i.setAttribute("type","button"),i.setAttribute("aria-label",c.closeLabel),i.innerHTML=c.closeText,f.appendChild(i),j=document.createElement("div"),j.className="tobii__counter",f.appendChild(j),document.body.appendChild(f)},L=function(){z[A].slider=document.createElement("div"),z[A].slider.className="tobii__slider",f.appendChild(z[A].slider)},M=function(a){for(var b in D)if(Object.prototype.hasOwnProperty.call(D,b)&&D[b].checkSupport(a)){var c=document.createElement("div"),d=document.createElement("div");c.className="tobii__slider-slide",c.style.position="absolute",c.style.left=100*z[A].x+"%",D[b].init(a,d),c.appendChild(d),z[A].slider.appendChild(c),z[A].sliderElements.push(c),++z[A].x;break}},N=function(a,b){if(B=null===B?A:B,ra()||a||(a=0),ra()){if(!a)throw new Error("Ups, Tobii is aleady open.");if(a===z[B].currentIndex)throw new Error("Ups, slide "+a+" is already selected.")}if(-1===a||a>=z[B].elementsLength)throw new Error("Ups, I can't find slide "+a+".");c.hideScrollbar&&(document.documentElement.classList.add("tobii-is-open"),document.body.classList.add("tobii-is-open")),na(),c.close||(i.disabled=!1,i.setAttribute("aria-hidden","true")),o=document.activeElement,z[B].currentIndex=a,Y(),ja(),Q(z[B].currentIndex),f.setAttribute("aria-hidden","false"),pa(),P(z[B].currentIndex+1),P(z[B].currentIndex-1),setTimeout(function(){z[B].slider.classList.add("tobii__slider--animate")},1e3),b&&b.call(this)},O=function(a){if(!ra())throw new Error("Tobii is already closed.");c.hideScrollbar&&(document.documentElement.classList.remove("tobii-is-open"),document.body.classList.remove("tobii-is-open")),ka(),o.focus();var b=z[B].sliderElements[z[B].currentIndex].querySelector("[data-type]"),d=b.getAttribute("data-type");D[d].onLeave(b),D[d].onCleanup(b),f.setAttribute("aria-hidden","true"),z[B].currentIndex=0,z[B].slider.classList.remove("tobii__slider--animate"),a&&a.call(this)},P=function(a){if(void 0!==z[B].sliderElements[a]){var b=z[B].sliderElements[a].querySelector("[data-type]"),c=b.getAttribute("data-type");D[c].onPreload(b)}},Q=function(a){if(void 0!==z[B].sliderElements[a]){var b=z[B].sliderElements[a].querySelector("[data-type]"),c=b.getAttribute("data-type");D[c].onLoad(b)}},R=function(a){0<z[B].currentIndex&&(T(z[B].currentIndex),Q(--z[B].currentIndex),pa("left"),U(z[B].currentIndex+1),P(z[B].currentIndex-1),a&&a.call(this))},S=function(a){z[B].currentIndex<z[B].elementsLength-1&&(T(z[B].currentIndex),Q(++z[B].currentIndex),pa("right"),U(z[B].currentIndex-1),P(z[B].currentIndex+1),a&&a.call(this))},T=function(a){if(void 0!==z[B].sliderElements[a]){var b=z[B].sliderElements[a].querySelector("[data-type]"),c=b.getAttribute("data-type");D[c].onLeave(b)}},U=function(a){if(void 0!==z[B].sliderElements[a]){var b=z[B].sliderElements[a].querySelector("[data-type]"),c=b.getAttribute("data-type");D[c].onCleanup(b)}},V=function(){B=null===B?A:B,r=-z[B].currentIndex*f.offsetWidth,z[B].slider.style.transform="translate3d("+r+"px, 0, 0)",s=r},W=function(){j.textContent=z[B].currentIndex+1+"/"+z[B].elementsLength},X=function(a){var b=null;c.nav?(g.disabled=!1,h.disabled=!1,"left"===a?g.focus():h.focus(),1===z[B].elementsLength?(g.disabled=!0,h.disabled=!0,c.close&&i.focus()):(0===z[B].currentIndex&&(g.disabled=!0,h.focus()),z[B].currentIndex===z[B].elementsLength-1&&(h.disabled=!0,g.focus()))):c.close&&i.focus(),b=f.querySelectorAll(".tobii > button:not(:disabled)"),p=b[0],q=1===b.length?b[0]:b[b.length-1]},Y=function(){k={startX:0,endX:0,startY:0,endY:0}},Z=function(){var a=k.endX-k.startX,d=k.endY-k.startY,e=b(a),f=b(d);0<a&&e>c.threshold&&0<z[B].currentIndex?R():0>a&&e>c.threshold&&z[B].currentIndex!==z[B].elementsLength-1?S():0>d&&f>c.threshold&&c.swipeClose?O():V()},$=function(){t||(t=!0,d.requestAnimationFrame(function(){V(),t=!1}))},_=function(a){a.preventDefault(),B=G(this),N(z[B].gallery.indexOf(this))},aa=function(a){a.target===g?R():a.target===h?S():(a.target===i||"tobii__slider-slide"===a.target.className&&c.docClose)&&O(),a.stopPropagation()},ba=function(a){9===a.keyCode||"Tab"===a.code?a.shiftKey?document.activeElement===p&&(q.focus(),a.preventDefault()):document.activeElement===q&&(p.focus(),a.preventDefault()):27===a.keyCode||"Escape"===a.code?(a.preventDefault(),O()):37===a.keyCode||"ArrowLeft"===a.code?(a.preventDefault(),R()):(39===a.keyCode||"ArrowRight"===a.code)&&(a.preventDefault(),S())},ca=function(a){ta(a.target)||(a.stopPropagation(),n=!0,k.startX=a.touches[0].pageX,k.startY=a.touches[0].pageY,z[B].slider.classList.add("tobii__slider--is-dragging"))},da=function(a){a.stopPropagation(),n&&(a.preventDefault(),k.endX=a.touches[0].pageX,k.endY=a.touches[0].pageY,ia())},ea=function(a){a.stopPropagation(),n=!1,z[B].slider.classList.remove("tobii__slider--is-dragging"),k.endX&&(l=!1,m=!1,Z()),Y()},fa=function(a){ta(a.target)||(a.preventDefault(),a.stopPropagation(),n=!0,k.startX=a.pageX,k.startY=a.pageY,z[B].slider.classList.add("tobii__slider--is-dragging"))},ga=function(a){a.preventDefault(),n&&(k.endX=a.pageX,k.endY=a.pageY,ia())},ha=function(a){a.stopPropagation(),n=!1,z[B].slider.classList.remove("tobii__slider--is-dragging"),k.endX&&(l=!1,m=!1,Z()),Y()},ia=function(){var a=Math.round;0<b(k.startX-k.endX)&&!m&&c.swipeClose?(z[B].slider.style.transform="translate3d("+(s-a(k.startX-k.endX))+"px, 0, 0)",l=!0,m=!1):0<b(k.startY-k.endY)&&!l&&(z[B].slider.style.transform="translate3d("+s+"px, -"+a(k.startY-k.endY)+"px, 0)",l=!1,m=!0)},ja=function(){c.keyboard&&d.addEventListener("keydown",ba),d.addEventListener("resize",$),f.addEventListener("click",aa),c.draggable&&(sa()&&(f.addEventListener("touchstart",ca),f.addEventListener("touchmove",da),f.addEventListener("touchend",ea)),f.addEventListener("mousedown",fa),f.addEventListener("mouseup",ha),f.addEventListener("mousemove",ga))},ka=function(){c.keyboard&&d.removeEventListener("keydown",ba),d.removeEventListener("resize",$),f.removeEventListener("click",aa),c.draggable&&(sa()&&(f.removeEventListener("touchstart",ca),f.removeEventListener("touchmove",da),f.removeEventListener("touchend",ea)),f.removeEventListener("mousedown",fa),f.removeEventListener("mouseup",ha),f.removeEventListener("mousemove",ga))},la=function(a,b){return a.getAttribute("data-type")===b},ma=function(a){var b=a.querySelectorAll("src");b&&Array.prototype.forEach.call(b,function(a){a.setAttribute("src","")})},na=function(){c.draggable&&1<z[B].elementsLength&&!z[B].slider.classList.contains("tobii__slider--is-draggable")&&z[B].slider.classList.add("tobii__slider--is-draggable"),!c.nav||1===z[B].elementsLength||"auto"===c.nav&&sa()?(g.setAttribute("aria-hidden","true"),h.setAttribute("aria-hidden","true")):(g.setAttribute("aria-hidden","false"),h.setAttribute("aria-hidden","false")),c.counter&&1!==z[B].elementsLength?j.setAttribute("aria-hidden","false"):j.setAttribute("aria-hidden","true")},oa=function(){for(var a in z)Object.prototype.hasOwnProperty.call(z,a)&&(z[a].slider.style.display=B===a?"block":"none")},pa=function(a){oa(),V(),W(),X(a)},qa=function(a){ra()&&O();var b=Object.entries(z);Array.prototype.forEach.call(b,function(a){var b=a[1].gallery;Array.prototype.forEach.call(b,function(a){J(a)})}),f.parentNode.removeChild(f),z={},A=B=null,e=0,a&&a.call(this)},ra=function(){return"false"===f.getAttribute("aria-hidden")},sa=function(){return"ontouchstart"in window},ta=function(a){return-1!==["TEXTAREA","OPTION","INPUT","SELECT"].indexOf(a.nodeName)||a===g||a===h||a===i||1===z[B].elementsLength},ua=function(){return z[B].currentIndex},va=function(){return null===B?A:B},wa=function(a){if(ra())throw new Error("Ups, I can't do this. Tobii is open.");if(a){if(a&&!Object.prototype.hasOwnProperty.call(z,a))throw new Error("Ups, I don't have a group called \""+a+"\".");B=a}};return E(a),{open:N,prev:R,next:S,close:O,add:F,remove:J,destroy:qa,isOpen:ra,currentSlide:ua,selectGroup:wa,currentGroup:va}}});
