/*! Editable 0.0.10
 * © 2016-2017 <PERSON>
 */
/**
 * @summary     Editable
 * @description Allow editing of cells and rows
 * @version     0.0.10
 * @file        datatable.editable.js
 * <AUTHOR>
 * @contact     <EMAIL>
 * @copyright   Copyright 2016-2017 <PERSON>
 *
 * Double-click a cell to edit and hit enter to submit.
 * Right click to show context menu of editor options (Edit Cell, Edit Row, Remove Row).
 *
 * This source file is free software, available under the following license:
 *   MIT license - https://github.com/Mobius1/Vanilla-DataTables/blob/master/LICENSE
 *
 * This source file is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the license files for details.
 *
 * For details please refer to: https://github.com/Mobius1/Vanilla-DataTables
 */
.datatable-editor-modal,.datatable-editor-modal.closed{animation:250ms ease 0s fadeIn}.datatable-editor-modal{position:absolute;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,.6)}.datatable-editor-inner,.datatable-editor-modal.closed .datatable-editor-inner{animation:250ms ease 0s slideIn}.datatable-editor-inner{width:30%;margin:10% auto;background-color:#fff;border-radius:5px}.datatable-editor-header{position:relative;border-bottom:1px solid #ccc}.datatable-editor-header h4{font-size:20px;margin:0}.datatable-editor-header button{position:absolute;right:10px;top:10px;background-color:transparent;border:none;cursor:pointer;font-size:24px;padding:5px;line-height:1;opacity:.6}.datatable-editor-input,.datatable-editor-save{font-size:inherit;font-family:inherit;font-weight:inherit}.datatable-editor-header button:hover{opacity:1}.datatable-editor-header{padding:15px 30px}.datatable-editor-block{padding:15px 60px}.datatable-editor-row{margin:0 0 15px}.datatable-editor-row:last-child{margin:0 0 5px;text-align:right}.datatable-editor-label{width:25%;text-align:right;padding:0 15px}.datatable-editor-input,.datatable-editor-label{display:inline-block}.datatable-editor-input{padding:4px 6px;border:1px solid #ccc;width:100%;box-sizing:border-box;margin:-5px 0;color:inherit}.datatable-editor-row .datatable-editor-input{margin:0;width:75%}.datatable-editor-save{background-color:#27ae60;border:1px solid #27ae60;padding:6px 12px;color:#fff;cursor:pointer;border-radius:3px}.datatable-editor-save:hover{background-color:#2cc36b;border-color:#2cc36b}.datatable-editor-wrapper{position:absolute}.datatable-editor-menu{background:#fff;border-radius:3px;margin:0;min-width:220px;padding:5px 0;box-shadow:0 0 10px 2px #aaa}.datatable-editor-menu li{list-style:none}.datatable-editor-menu a{box-sizing:border-box;color:inherit;display:block;padding:5px 15px;text-decoration:none;width:100%}.datatable-editor-menu a:hover{background-color:#ddd}.datatable-editor-separator{border-bottom:1px solid #aaa;margin:5px 0}@keyframes fadeIn{from{opacity:0}to{opacity:1}}@keyframes slideIn{from{opacity:0;transform:translate3d(0,-10%,0)}to{opacity:1;transform:translate3d(0,0,0)}}