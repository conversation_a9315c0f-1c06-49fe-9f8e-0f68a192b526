/*! Editable 0.0.10
 * © 2016-2017 <PERSON>
 */
/**
 * @summary     Editable
 * @description Allow editing of cells and rows
 * @version     0.0.10
 * @file        datatable.editable.js
 * <AUTHOR>
 * @contact     <EMAIL>
 * @copyright   Copyright 2016-2017 <PERSON>
 *
 * Double-click a cell to edit and hit enter to submit.
 * Right click to show context menu of editor options (Edit Cell, Edit Row, Remove Row).
 *
 * This source file is free software, available under the following license:
 *   MIT license - https://github.com/Mobius1/Vanilla-DataTables/blob/master/LICENSE
 *
 * This source file is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the license files for details.
 *
 * For details please refer to: https://github.com/Mobius1/Vanilla-DataTables
 */
window.DataTable&&"function"==typeof window.DataTable&&DataTable.extend("editable",function(t,e){var i=this,n={classes:{row:"datatable-editor-row",form:"datatable-editor-form",item:"datatable-editor-item",menu:"datatable-editor-menu",save:"datatable-editor-save",block:"datatable-editor-block",close:"datatable-editor-close",inner:"datatable-editor-inner",input:"datatable-editor-input",label:"datatable-editor-label",modal:"datatable-editor-modal",action:"datatable-editor-action",header:"datatable-editor-header",wrapper:"datatable-editor-wrapper",container:"datatable-editor-container",separator:"datatable-editor-separator"},hiddenColumns:!1,contextMenu:!0,menuItems:[{text:"<span class='mdi mdi-lead-pencil'></span> Edit Cell",action:function(t){this.editCell()}},{text:"<span class='mdi mdi-lead-pencil'></span> Edit Row",action:function(t){this.editRow()}},{separator:!0},{text:"<span class='mdi mdi-delete'></span> Remove Row",action:function(t){confirm("Are you sure?")&&this.removeRow()}}]},a=function(t,e,i){t.addEventListener(e,i,!1)},s=function(t,e,i){t.removeEventListener(e,i)},o=function(t,e){return t&&t!==document.body&&(e(t)?t:o(t.parentNode,e))},d=function(t,e,i){var n;return function(){var a=this,s=arguments,o=i&&!n;clearTimeout(n),n=setTimeout(function(){n=null,i||t.apply(a,s)},e),o&&t.apply(a,s)}},l=function(t,i){this.target=t,this.config=e.extend(n,i)};return l.prototype.init=function(){var t=this,n=t.config;e.classList.add(i.wrapper,"datatable-editable"),n.contextMenu&&(t.container=e.createElement("div",{id:n.classes.container}),t.wrapper=e.createElement("div",{class:n.classes.wrapper}),t.menu=e.createElement("ul",{class:n.classes.menu}),n.menuItems&&n.menuItems.length&&n.menuItems.forEach(function(i){var s=e.createElement("li",{class:i.separator?n.classes.separator:n.classes.item});if(!i.separator){var o=e.createElement("a",{class:n.classes.action,href:i.url||"#",html:i.text});s.appendChild(o),i.action&&"function"==typeof i.action&&a(o,"click",function(e){e.preventDefault(),i.action.call(t,e)})}t.menu.appendChild(s)}),t.wrapper.appendChild(t.menu),t.container.appendChild(t.wrapper),t.update()),t.data={},t.closed=!0,t.editing=!1,t.editingRow=!1,t.editingCell=!1,t.bindEvents(),setTimeout(function(){i.emit("editable.init")},10)},l.prototype.bindEvents=function(){this.events={context:this.context.bind(this),update:this.update.bind(this),dismiss:this.dismiss.bind(this),keydown:this.keydown.bind(this),double:this.double.bind(this)},a(this.target,"dblclick",this.events.double),a(document,"click",this.events.dismiss),a(document,"keydown",this.events.keydown),this.config.contextMenu&&(a(this.target,"contextmenu",this.events.context),this.events.reset=d(this.events.update,50),a(window,"resize",this.events.reset),a(window,"scroll",this.events.reset))},l.prototype.context=function(t){this.event=t;var e=this.target.contains(t.target);if(!this.disabled&&e){t.preventDefault();var i=t.pageX,n=t.pageY;i>this.limits.x&&(i-=this.rect.width),n>this.limits.y&&(n-=this.rect.height),this.wrapper.style.top=n+"px",this.wrapper.style.left=i+"px",this.openMenu(),this.update()}},l.prototype.double=function(t){if(!this.editing){var e=o(t.target,function(t){return"TD"===t.nodeName});e&&(this.editCell(e),t.preventDefault())}},l.prototype.keydown=function(t){this.editing&&this.data&&(13===t.keyCode?this.editingCell?this.saveCell():this.editingRow&&this.saveRow():27===t.keyCode&&this.saveCell(this.data.content))},l.prototype.editCell=function(t){if("TD"===(t=t||o(this.event.target,function(t){return"TD"===t.nodeName})).nodeName&&!this.editing){var i=this;i.data={cell:t,content:t.innerHTML,input:e.createElement("input",{type:"text",class:i.config.classes.input,value:t.innerHTML})},t.innerHTML="",t.appendChild(i.data.input),setTimeout(function(){i.data.input.focus(),i.data.input.selectionStart=i.data.input.selectionEnd=i.data.input.value.length,i.editing=!0,i.editingCell=!0,i.closeMenu()},10)}},l.prototype.saveCell=function(t,e){e=e||this.data.cell,t=t||this.data.input.value;var n=e.data;e.innerHTML=t.trim(),this.data={},this.editing=this.editingCell=!1,i.emit("editable.save.cell",t,n)},l.prototype.editRow=function(t){if("TR"===(t=t||o(this.event.target,function(t){return"TR"===t.nodeName})).nodeName&&!this.editing){var n=this,a=n.config;t=a.hiddenColumns?i.data[t.dataIndex]:i.activeRows[t.dataIndex];var s=["<div class='"+a.classes.inner+"'>","<div class='"+a.classes.header+"'>","<h4>Editing row</h4>","<button class='"+a.classes.close+"' type='button' data-editor-close>×</button>"," </div>","<div class='"+a.classes.block+"'>","<form class='"+a.classes.form+"'>","<div class='"+a.classes.row+"'>","<button class='"+a.classes.save+"' type='button' data-editor-save>Save</button>","</div>","</form>","</div>","</div>"].join(""),d=e.createElement("div",{class:a.classes.modal,html:s}),l=d.firstElementChild.lastElementChild.firstElementChild;[].slice.call(t.cells).forEach(function(t,n){l.insertBefore(e.createElement("div",{class:a.classes.row,html:["<div class='datatable-editor-row'>","<label class='"+a.classes.label+"'>"+i.labels[a.hiddenColumns?n:i.activeHeadings[n].originalCellIndex]+"</label>","<input class='"+a.classes.input+"' value='"+t.innerHTML+"' type='text'>","</div>"].join("")}),l.lastElementChild)}),this.modal=d,this.openModal();var r=[].slice.call(l.elements);r.pop(),n.data={row:t,inputs:r},this.editing=!0,this.editingRow=!0,d.addEventListener("click",function(t){var e=t.target;e.hasAttribute("data-editor-close")?n.closeModal():e.hasAttribute("data-editor-save")&&n.saveRow()}),n.closeMenu()}},l.prototype.saveRow=function(t,e){var n=this,a=n.config;t=t||n.data.inputs.map(function(t){return t.value.trim()}),e=e||n.data.row;var s=[].slice.call(e.cells).map(function(t){return t.data});[].slice.call(e.cells).forEach(function(n,s){(n=i.data[e.dataIndex].cells[a.hiddenColumns?s:i.activeHeadings[s].originalCellIndex]).innerHTML=n.data=t[s]}),i.columns().rebuild(),this.closeModal(),i.emit("editable.save.row",t,s)},l.prototype.openModal=function(){!this.editing&&this.modal&&document.body.appendChild(this.modal)},l.prototype.closeModal=function(){this.editing&&this.modal&&(document.body.removeChild(this.modal),this.modal=this.editing=this.editingRow=!1)},l.prototype.removeRow=function(t){t?(t instanceof Element&&"TR"===t.nodeName&&void 0!==t.dataIndex&&(t=t.dataIndex),i.rows().remove(t),this.closeMenu()):(t=o(this.event.target,function(t){return"TR"===t.nodeName}))&&void 0!==t.dataIndex&&(i.rows().remove(t.dataIndex),this.closeMenu())},l.prototype.update=function(){var t=window.scrollX||window.pageXOffset,e=window.scrollY||window.pageYOffset;this.rect=this.wrapper.getBoundingClientRect(),this.limits={x:window.innerWidth+t-this.rect.width,y:window.innerHeight+e-this.rect.height}},l.prototype.dismiss=function(t){var e=!0;this.config.contextMenu&&(e=!this.wrapper.contains(t.target),this.editing&&(e=!this.wrapper.contains(t.target)&&t.target!==this.data.input)),e&&(this.editing&&this.saveCell(this.data.content),this.closeMenu())},l.prototype.openMenu=function(){this.config.contextMenu&&(document.body.appendChild(this.container),this.closed=!1,i.emit("editable.context.open"))},l.prototype.closeMenu=function(){this.config.contextMenu&&!this.closed&&(this.closed=!0,document.body.removeChild(this.container),i.emit("editable.context.close"))},l.prototype.destroy=function(){s(this.target,"dblclick",this.events.double),s(this.target,"contextmenu",this.events.context),s(document,"click",this.events.dismiss),s(document,"keydown",this.events.keydown),s(window,"resize",this.events.reset),s(window,"scroll",this.events.reset),document.body.removeChild(this.container)},new l(this.body,t)});